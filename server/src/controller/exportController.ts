import { Request, Response } from 'express';
import * as XLSX from 'xlsx';
import { fetchExamApplicationsForExport, fetchQuestionBankForExport } from '../services/exportService';

// Helper function to generate Excel response (as provided by user)
const generateExcelResponse = (
  data: any[],
  worksheetName: string,
  fileName: string,
  res: Response
) => {
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
  const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

  res.send(excelBuffer);
};

// Export exam applicants to Excel
export const exportExamApplicants = async (req: Request, res: Response): Promise<void> => {
  try {
    const { examId } = req.params;

    // Validate examId
    if (!examId || isNaN(Number(examId))) {
      res.status(400).json({
        message: 'Valid examId is required',
      });
      return;
    }

    // Fetch exam applicants data
    const applicantsData = await fetchExamApplicationsForExport(Number(examId));

    // Check if there are any applicants
    if (applicantsData.length === 0) {
      res.status(404).json({
        message: 'No applicants found for this exam',
      });
      return;
    }

    // Generate filename with examId and timestamp
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const fileName = `Exam_${examId}_Applicants_${timestamp}.xlsx`;

    // Generate and send Excel file
    generateExcelResponse(
      applicantsData,
      'Exam Applicants',
      fileName,
      res
    );

  } catch (error: any) {
    console.error('Error exporting exam applicants:', error);
    res.status(500).json({
      message: 'Error exporting exam applicants',
      error: error.message,
    });
  }
};

// Export question bank to Excel
export const exportQuestionBank = async (req: Request, res: Response): Promise<void> => {
  try {
    const { medium, standard, level, subject } = req.query;

    // Build filters object
    const filters: {
      medium?: string;
      standard?: string;
      level?: string;
      subject?: string;
    } = {};

    if (typeof medium === "string") filters.medium = medium;
    if (typeof standard === "string") filters.standard = standard;
    if (typeof level === "string") filters.level = level;
    if (typeof subject === "string") filters.subject = subject;

    // Fetch question bank data
    const questionBankData = await fetchQuestionBankForExport(filters);

    // Check if there are any questions
    if (questionBankData.length === 0) {
      res.status(404).json({
        message: 'No questions found with the specified filters',
      });
      return;
    }

    // Generate filename with filters and timestamp
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const filterString = Object.entries(filters)
      .filter(([_, value]) => value)
      .map(([key, value]) => `${key}-${value}`)
      .join('_');

    const fileName = filterString
      ? `QuestionBank_${filterString}_${timestamp}.xlsx`
      : `QuestionBank_All_${timestamp}.xlsx`;

    // Generate and send Excel file
    generateExcelResponse(
      questionBankData,
      'Question Bank',
      fileName,
      res
    );

  } catch (error: any) {
    console.error('Error exporting question bank:', error);
    res.status(500).json({
      message: 'Error exporting question bank',
      error: error.message,
    });
  }
};
