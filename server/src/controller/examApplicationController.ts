import { Request, Response } from 'express';
import * as examApplicationService from '../services/examApplicationServices';

// Apply for an exam
export const applyForExam = async (req: Request, res: Response): Promise<void> => {
  const { examId, applicantId } = req.body;

  if (!examId || !applicantId || isNaN(Number(examId))) {
    res.status(400).json({
      message: 'examId and applicantId are required, and examId must be a valid number',
    });
    return;
  }

  try {
    const { application, joinedApplicantsCount } = await examApplicationService.applyForExam(
      Number(examId),
      applicantId
    );
    res.status(201).json({
      message: 'Successfully applied for the exam',
      application,
      joinedApplicantsCount,
    });
  } catch (error: any) {
    res.status(400).json({
      message: 'Error applying for exam',
      error: error.message,
    });
  }
};



export const getApplicants = async (req:Request, res:Response) => {
  try {
    const { examId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const result = await examApplicationService.getApplicants(examId, parseInt(page as string), parseInt(limit as string));
    res.json(result);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Failed to get applicants' });
  }
};
