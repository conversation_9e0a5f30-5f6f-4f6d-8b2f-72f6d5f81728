import { calculateRankings } from '../services/uwhizResultServices';
import { Request,Response } from 'express';

export const getRankingsByExamId = async (req: Request, res: Response):Promise<any> => {
  try {
    const examId = parseInt(req.params.examId);
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (isNaN(examId)) {
      return res.status(400).json({ error: 'Invalid exam ID' });
    }

    const result = await calculateRankings(examId, page, limit);
    return res.status(200).json({
      success: true,
      data: result.rankings,
      pagination: {
        totalItems: result.totalItems,
        totalPages: result.totalPages,
        currentPage: result.currentPage,
      },
    });
  } catch (error: any) {
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal server error',
    });
  }
};