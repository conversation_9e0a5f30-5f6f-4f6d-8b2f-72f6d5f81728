import { Request, Response } from 'express';
import { getMockQuestionsForStudent } from '../services/uwhizMockExamService';

export const getMockQuestions = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.params.studentId;
    const questions = await getMockQuestionsForStudent(studentId);
    res.status(200).json(questions);
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Something went wrong' });
  }
};