import { Router } from "express";
import {addQuizTeminationController,getTerminatedStudentsController} from  "../controller/quizTeminationController";
import { authClientMiddleware } from "../middleware/clientAuth";

const quizTeminationRouter = Router();

quizTeminationRouter.post('/',authClientMiddleware,addQuizTeminationController);
quizTeminationRouter.get('/:examId',authClientMiddleware,getTerminatedStudentsController)

export default quizTeminationRouter; 