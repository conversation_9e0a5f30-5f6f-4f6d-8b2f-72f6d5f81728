// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum MEDIUM {
  GUJARATI
  ENGLISH
}

enum LEVEL {
  EASY
  MEDIUM
  HARD
}

enum CORRECTANS{
  optionOne
  optionTwo
  optionThree
  optionFour
}

model QuestionBank {
  id             String           @id @default(uuid())
  question       String
  optionOne      String
  optionTwo      String
  optionThree    String
  optionFour     String
  correctAnswer  String
  medium         MEDIUM
  standard       String
  subject        String
  level          LEVEL
  classID        String?
  chapter        String?
  createdAt      DateTime?        @default(now())
  updatedAt      DateTime?        @updatedAt
  saveExamAnswer SaveExamAnswer[]
}

model Exam {
  id                      Int                @id @default(autoincrement())
  exam_name               String
  start_date              DateTime
  duration                Int
  marks                   Decimal
  total_student_intake    Int
  level                   String
  createdAt               DateTime           @default(now())
  updatedAt               DateTime           @updatedAt
  total_questions         Int
  coins_required          Int?
  start_registration_date DateTime?
  exam_type               ExamType?
  UwhizPriceRank          UwhizPriceRank[]
  examApplication         ExamApplication[]
  subjectPrefrence        SubjectPrefrence[]
  levelPrefrence          LevelPrefrences[]
  saveExamAnswer          SaveExamAnswer[]
  quizTermination         QuizTemination[]  
}

enum ExamType {
  CLASSES
  STUDENTS
}

model UwhizPriceRank {
  id     String @id @default(uuid())
  examId Int
  rank   Int
  price  Int
  exam   Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model ExamApplication {
  id          String   @id @default(uuid())
  examId      Int
  applicantId String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  exam        Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model SubjectPrefrence {
  id        String @id @default(uuid())
  examId    Int
  subject   String
  weightage Int
  exam      Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model LevelPrefrences {
  id              String @id @default(uuid())
  examId          Int
  level           String
  weightage       Int
  timePerQuestion Int?
  exam            Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
}

model SaveExamAnswer {
  id           String       @id @default(uuid())
  studentId    String
  examId       Int
  questionId   String
  selectedAns  String?
  exam         Exam         @relation(fields: [examId], references: [id], onDelete: Cascade)
  questionBank QuestionBank @relation(fields: [questionId], references: [id])

  @@unique([studentId, examId, questionId])
}

model QuizTemination {
  id        String @id @default(uuid())
  applicantId String
  examId    Int
  reason    String
  exam            Exam   @relation(fields: [examId], references: [id], onDelete: Cascade)
  createdAt      DateTime?        @default(now())
  updatedAt      DateTime?        @updatedAt
}

model mockExamQuestionBank {
  id             String           @id @default(uuid())
  question       String
  optionOne      String
  optionTwo      String
  optionThree    String
  optionFour     String
  correctAnswer  CORRECTANS
  quetionDate    DateTime        @default(now())
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
}

model mockExamResult {
  id             String           @id @default(uuid())
  studentId      String
  score          Int
  coinEarnings   Int
  streakId     String?
  streak       mockExamStreak? @relation(fields: [streakId], references: [id])
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
}

model mockExamTermination {
  id             String           @id @default(uuid())
  studentId      String
  reason         String
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
}

model mockExamStreak {
  id             String    @id @default(uuid())
  studentId      String
  streakCount    Int       @default(0)
  lastAttempt    DateTime
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  badges         mockExamBadge[]
  results        mockExamResult[]   @relation()
}
model mockExamBadge {
  id              String    @id @default(uuid())
  studentId       String
  Badge           String    
  streakId        String
  streak          mockExamStreak @relation(fields: [streakId], references: [id])
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}
