import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';
import cookieParser from 'cookie-parser';
import questionBankRoutes from './routes/questionBankRoutes';
import examApplicationRoutes from './routes/examApplicantRoutes';
import uwhizPriceRank from './routes/uwhizPriceRankRoutes';
import exam from './routes/examRoutes';
import exportRoutes from './routes/exportRoutes';
import subjectPrefrenceRoutes from './routes/subjectPrefrenceRoutes';
import levelPrefrenceRoutes from './routes/levelPrefrenceRoutes';
import {questionForStudentsRoutes} from './routes/questionForStudentRoutes';
import uwhizSaveAnswerRoutes from './routes/uwhizSaveAnswerRoutes';
import quizTeminationRouter from './routes/quizTerminationRoutes';
import uwhizPreventReattempRoutes from './routes/uwhizPreventReattemptRoutes';
import mockQuestionForStudentsRoutes from './routes/uwhizMockExamRoutes';
import mockExamQuestionBank from './routes/mockExamQuestionBankRoutes';
import mockExamResultRoutes from './routes/mockExamResultRoutes';
import mockExamTeminationRouter from './routes/uwhizMockExamTerminateRoutes';
import mockExamStreakRoutes from './routes/mockExamStreakRoutes';
import leaderboardRouter from './routes/mockExamLeaderBoardRoutes';


dotenv.config();

const app = express();
const PORT = process.env.PORT || 4006;


const allowedOrigins = [process.env.FRONTEND_URL, process.env.ADMIN_URL, '*','192.168.1.169'];
app.use(helmet());

app.use(
  cors({
    origin: (origin, callback) => {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    credentials: true,
  })
);

app.use(cookieParser());
app.use(express.json());

const limiter = rateLimit({
  windowMs: 1 * 60 * 1000,
  limit: 3000,
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

app.disable('x-powered-by');

app.use('/questionBank', questionBankRoutes);
app.use('/examApplication',examApplicationRoutes);
app.use('/uwhizPriceRank',uwhizPriceRank);
app.use('/exams', exam);
app.use('/export', exportRoutes);
app.use('/subjectPrefrence',subjectPrefrenceRoutes);
app.use('/levelPrefrence',levelPrefrenceRoutes);
app.use('/questionForStudentRouter',questionForStudentsRoutes);
app.use('/saveExamAnswer',uwhizSaveAnswerRoutes);
app.use('/quizTermination',quizTeminationRouter);
app.use('/check-attempt',uwhizPreventReattempRoutes)
app.use('/mock-exam',mockQuestionForStudentsRoutes);
app.use('/mock-exam-questionBank', mockExamQuestionBank);
app.use('/mock-exam-result', mockExamResultRoutes);
app.use('/mock-exam-terminate', mockExamTeminationRouter);
app.use('/mock-exam-streak', mockExamStreakRoutes);
app.use('/mock-exam-leaderboard', leaderboardRouter);


app.use('/uploads', express.static(path.join(__dirname, '..', 'Uploads')));

app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

app.listen(4006, () => {
  console.log(`🚀 Server is running on port ${PORT}`);
});

export = app;