import prisma from '../config/prismaClient';
import axios from 'axios';

interface Ranking {
  studentId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  score: number;
  attempts: number;
  rank: number;
  totalQuestions: number;
}

export const calculateRankings = async (
  examId: number,
  page: number = 1,
  limit: number = 10
): Promise<any> => {
  try {
    if (page < 1) throw new Error('Page number must be at least 1');
    if (limit < 1) throw new Error('Limit must be at least 1');

    const answers = await prisma.saveExamAnswer.findMany({
      where: { examId },
      include: {
        questionBank: {
          select: { correctAnswer: true },
        },
      },
    });

    const exam = await prisma.exam.findUnique({
      where: { id: examId },
      select: { total_questions: true },
    });

    if (!exam || !exam.total_questions) {
      throw new Error('Exam not found or invalid total_questions');
    }
    const totalQuestions = exam.total_questions;

    if (!answers.length) {
      return { rankings: [], totalItems: 0, totalPages: 0, currentPage: page };
    }

    const scoreMap: { [studentId: string]: { score: number; attempts: number } } = {};
    for (const answer of answers) {
      if (!answer.studentId) {
        console.warn(`Skipping answer with missing studentId for exam ${examId}`);
        continue;
      }
      if (!scoreMap[answer.studentId]) {
        scoreMap[answer.studentId] = { score: 0, attempts: 0 };
      }
      scoreMap[answer.studentId].attempts++;
      if (
        answer.selectedAns &&
        answer.questionBank?.correctAnswer &&
        answer.selectedAns === answer.questionBank.correctAnswer
      ) {
        scoreMap[answer.studentId].score++;
      }
    }

    const scoreArray = Object.entries(scoreMap)
      .map(([studentId, { score, attempts }]) => ({
        studentId,
        score,
        attempts,
      }))
      .sort((a, b) => {
        if (a.score !== b.score) return b.score - a.score;
        return a.attempts - b.attempts;
      });

    if (!scoreArray.length) {
      return { rankings: [], totalItems: 0, totalPages: 0, currentPage: page };
    }

    const rankings: Ranking[] = [];
    let currentRank = 1;
    let currentScore = scoreArray[0].score;
    let currentAttempts = scoreArray[0].attempts;

    scoreArray.forEach(({ studentId, score, attempts }, index) => {
      if (index > 0 && (score !== currentScore || attempts !== currentAttempts)) {
        currentRank++;
        currentScore = score;
        currentAttempts = attempts;
      }
      rankings.push({ studentId, score, attempts, rank: currentRank, totalQuestions });
    });

    const applicantIds = rankings.map((r) => r.studentId);
    let studentData: any[] = [];
    try {
      const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`, {
        applicantIds,
      });
      studentData = response.data;
    } catch (error) {
      console.error(`Error fetching student data for exam ${examId}:`, error);
    }

    const enrichedRankings = rankings.map((ranking) => {
      const student = studentData.find((s: any) => s.id === ranking.studentId);
      return {
        ...ranking,
        firstName: student?.firstName || 'Unknown',
        lastName: student?.lastName || '',
        email: student?.email || 'N/A',
      };
    });

    const totalItems = enrichedRankings.length;
    const totalPages = Math.ceil(totalItems / limit);
    const startIndex = (page - 1) * limit;
    const paginatedRankings = enrichedRankings.slice(startIndex, startIndex + limit);

    return {
      rankings: paginatedRankings,
      totalItems,
      totalPages,
      currentPage: page,
    };
  } catch (error: any) {
    console.error(`Error calculating rankings for exam ${examId}:`, error.message);
    throw new Error(`Failed to calculate rankings: ${error.message}`);
  }
};