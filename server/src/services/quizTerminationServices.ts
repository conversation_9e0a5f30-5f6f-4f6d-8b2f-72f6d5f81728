import axios from "axios";
import prisma from "../config/prismaClient";

export const addQuizTerminationServices = async (examId:number,applicantId:string,reason:string) =>{
    const quizTerminationData = await prisma.quizTemination.create({
        data:{
            examId,
            reason,
            applicantId
        }
    });
    return quizTerminationData;
}


export const getQuizTerminationServices = async (examId:any, page:any, limit:any) => {
  const skip = (page - 1) * limit;

  const exam = await prisma.exam.findUnique({ where: { id: parseInt(examId) } });
  if (!exam) throw new Error('Exam not found');

  const applications = await prisma.quizTemination.findMany({
    where: { examId: parseInt(examId) },
    skip,
    take: limit,
    orderBy: { createdAt: 'desc' },
  });

  const applicantIds = applications.map(app => app.applicantId);

    const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhiz-terminated-students`, {
    applicantIds,
  });


  const dataWithCreatedAt = response.data.map((applicant: any, index: number) => ({
    ...applicant,
    createdAt: applications[index].createdAt,
    reason:applications[index].reason,
  }));

  return {
    total: await prisma.quizTemination.count({ where: { examId: parseInt(examId) } }),
    page,
    limit,
    data: dataWithCreatedAt,
  }
}