import prisma from "../config/prismaClient";

export const addQuizTerminationServices = async (studentId:string,reason:string) =>{
    const quizTerminationData = await prisma.mockExamTermination.create({
        data:{
            reason,
            studentId
        }
    });
    return quizTerminationData;
}

export const countTerminations = async (studentId: any) => {
 try{
    const terminationCount = await prisma.mockExamTermination.count({
      where: {
        studentId: studentId,
      },
    });

    return terminationCount;
  } catch (error) {
    console.error('Error checking exam attempt:', error);
    return false; 
  }
}