import prisma from "../config/prismaClient";
import axios from "axios";

export const applyForExam = async (examId: number, applicantId: string) => {
  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: {
      id: true,
      exam_name: true,
      coins_required: true,
      total_student_intake: true,
      exam_type: true,
    },
  });

  if (!exam) {
    throw new Error("Exam not found");
  }

  const existingApplication = await prisma.examApplication.findFirst({
    where: {
      examId,
      applicantId,
    },
  });

  if (existingApplication) {
    throw new Error("This applicant has already applied for the exam");
  }

  const joinedApplicantsCount = await prisma.examApplication.count({
    where: { examId },
  });

  if (joinedApplicantsCount >= exam.total_student_intake) {
    throw new Error(
      "Maximum number of applicants already reached for this exam"
    );
  }

  if (exam.coins_required && exam.coins_required > 0) {
    const modelType = exam.exam_type === 'CLASSES' ? 'CLASS' : 'STUDENT'; 

    try {
      const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhizCoinDeduction/validate-and-deduct`, {
        modelId: applicantId,
        modelType,
        coinsRequired: exam.coins_required,
      });

      if (!response.data.success) {
        throw new Error('Failed to deduct coins');
      }
    } catch (error:any) {
      throw new Error(error.response?.data?.error || 'Error validating coins');
    }
  }

  // Insert record into ExamApplication
  const application = await prisma.examApplication.create({
    data: {
      examId,
      applicantId,
    },
  });

  return {
    application,
    joinedApplicantsCount: joinedApplicantsCount + 1,
  };
};


// Get all applicants for an exam
export const getApplicants = async (examId:any, page:any, limit:any) => {
  const skip = (page - 1) * limit;

  const exam = await prisma.exam.findUnique({ where: { id: parseInt(examId) } });
  if (!exam) throw new Error('Exam not found');

  const applications = await prisma.examApplication.findMany({
    where: { examId: parseInt(examId) },
    skip,
    take: limit,
    orderBy: { createdAt: 'desc' },
  });

  const applicantIds = applications.map(app => app.applicantId);

  const response = await axios.post(`${process.env.UEST_BACKEND_URL}/uwhizApplicant/get-user-details`, {
    examType: exam.exam_type,
    applicantIds,
  });


  const dataWithCreatedAt = response.data.map((applicant: any, index: number) => ({
    ...applicant,
    createdAt: applications[index].createdAt,
  }));

  return {
    total: await prisma.examApplication.count({ where: { examId: parseInt(examId) } }),
    page,
    limit,
    data: dataWithCreatedAt,
  }
};
