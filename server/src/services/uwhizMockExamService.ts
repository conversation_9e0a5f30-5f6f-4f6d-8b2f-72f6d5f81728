import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
import NodeCache from 'node-cache';
import { toZonedTime } from 'date-fns-tz';

const quizCache = new NodeCache({ stdTTL: 86400, checkperiod: 60 });

export const getMockQuestionsForStudent = async (studentId: string): Promise<any[]> => {

    const cacheKey = 'mockExamQuestions-' + studentId;

    const cachedState = quizCache.get<any>(cacheKey);
    if (cachedState) {
        return cachedState;
    }
  
    await prisma.mockExamTermination.deleteMany({where:{studentId}})   

    const timeZone = 'Asia/Kolkata';

    const now = new Date();
    const startOfTodayIST = toZonedTime(
    new Date(now.getFullYear(), now.getMonth(), now.getDate()),
    timeZone
    );

    const endOfTodayIST = new Date(startOfTodayIST.getTime() + 24 * 60 * 60 * 1000 - 1);

    const mockExamQuestions = await prisma.mockExamQuestionBank.findMany({
        where: {
            quetionDate: {
                gte: startOfTodayIST,
                lte: endOfTodayIST,
            },
        },
        select: {
            id: true,
            question: true,
            optionOne: true,
            optionTwo: true,
            optionThree: true,
            optionFour: true,
            correctAnswer: true,
        }
    });

    const shuffledQuestions = mockExamQuestions.sort(() => Math.random() - 0.5).slice(0, 10);

    quizCache.set(cacheKey, shuffledQuestions);

    return shuffledQuestions;
} 